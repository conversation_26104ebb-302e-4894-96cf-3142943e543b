# HL7 Message Enhancement Implementation

## Overview
This implementation follows the specifications in `Context/HL7_POC_Spec.md` to enhance HL7 messages by adding missing OBX segments with patient data from the HMC_Livefeeds database.

## Implementation Details

### Core Functionality
The `MessageOptimize.cs` class now implements the following workflow:

1. **HL7 Message Reception**: Receives HL7 messages via Azure Blob Trigger
2. **Local Storage**: Saves original HL7 messages to `hl7-messages/original/` folder
3. **Patient Data Extraction**: Extracts MRN from PID.3 segment
4. **Database Query**: Queries HMC_Livefeeds.Patients table for missing patient data
5. **OBX Enhancement**: Adds missing OBX segments for required fields
6. **Enhanced Storage**: Saves enhanced HL7 messages to `hl7-messages/enhanced/` folder

### Required OBX Fields
The system checks for and adds these missing OBX segments:
- **QATAR_ID_EXP** (DT) - Qatar ID expiration date from `QidExpiryDate`
- **HC_EXP_DATE** (DT) - Healthcare card expiration date from `HcExpiryDate`
- **FAMILY_PHYSICIAN** (CD) - Family physician ID from `AssignedFamilyPhysicianId`
- **PRIM_ORG_NAME** (TX) - Primary organization name from `AssignedHcCode`

### Database Integration
- Uses Entity Framework Core with `HmcLiveFeedContext`
- Queries `Patients` table using Health Card Number (MRN)
- Handles database connection failures gracefully

### Local File Storage
- **Original Messages**: `hl7-messages/original/{filename}_{timestamp}_original.hl7`
- **Enhanced Messages**: `hl7-messages/enhanced/{filename}_{timestamp}_enhanced.hl7`
- Files are timestamped for audit trails and debugging

### Error Handling
- Database connection failures: Log error, return original message
- Patient not found: Log warning, return original message unchanged
- HL7 parsing failures: Log error, return error status
- File I/O errors: Log error but continue processing

## Key Changes Made

### 1. Updated MessageOptimize.cs
- Added dependency injection for `HmcLiveFeedService`
- Implemented HL7 enhancement logic according to POC specification
- Added local file storage for original and enhanced messages
- Removed JSON conversion (not required per specification)

### 2. Updated Program.cs
- Ensured `HmcLiveFeedService` is registered in DI container

### 3. Created Local Storage Structure
- `hl7-messages/original/` - For original HL7 messages
- `hl7-messages/enhanced/` - For enhanced HL7 messages

### 4. Updated Documentation
- Updated class documentation to reflect actual POC specification
- Added comprehensive inline comments for maintainability

## Testing Recommendations

1. **Unit Tests**: Test OBX field detection and enhancement logic
2. **Integration Tests**: Test database connectivity and patient data retrieval
3. **End-to-End Tests**: Test complete workflow with sample HL7 messages
4. **Error Handling Tests**: Test various failure scenarios

## Configuration Requirements

Ensure the following are configured in `local.settings.json`:
```json
{
  "Values": {
    "AzureWebJobsStorage": "your_storage_connection_string",
    "HmcLiveFeedsConnectionString": "your_database_connection_string"
  }
}
```

## Sample HL7 Enhancement

**Before Enhancement** (missing QATAR_ID_EXP):
```
MSH|^~\&|Millennium|HMC|RHAPSODY_ADT|HMC|20250518103740||ADT^A31|Q123|P|2.3
PID|1||**********^^^MRN^MRN^RHAPSODY_CON_SYS|...
OBX|1|CE|COUNTRY_RES||Qatar||||||
OBX|2|DT|HC EXP DATE||20280527||||||
```

**After Enhancement** (QATAR_ID_EXP added):
```
MSH|^~\&|Millennium|HMC|RHAPSODY_ADT|HMC|20250518103740||ADT^A31|Q123|P|2.3
PID|1||**********^^^MRN^MRN^RHAPSODY_CON_SYS|...
OBX|1|CE|COUNTRY_RES||Qatar||||||
OBX|2|DT|HC EXP DATE||20280527||||||
OBX|3|DT|QATAR_ID_EXP||20261030||||||
```
