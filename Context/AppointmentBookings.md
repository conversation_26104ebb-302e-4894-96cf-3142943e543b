```markdown
## AppointmentBookings Table Definition

**Data Source:** HMC Live Feeds QC Dev
**Database:** hmc_livefeeds
**Table:** AppointmentBookings

---

This table stores information about appointment bookings.

**Schema Definition:**

```sql
-- auto-generated definition
create table AppointmentBookings
(
    HC_Number                     varchar(20),
    QID                           varchar(20),
    Appointment_Id                bigint not null
        constraint PK_AppointmentBookings
            primary key,
    Initial_Appointment_Date_time datetime,
    Appointment_Date_time         datetime,
    Appointment_Type_code         varchar(300),
    Appointment_Status            varchar(100),
    Clinic_Type_code              varchar(300),
    Appointment_HC_Code           varchar(100),
    Appointment_Physician_Id      varchar(300),
    Appointment_duration          int,
    Appointment_Consultation_Type varchar(100),
    Insert_Date                   datetime,
    Update_Date                   datetime,
    Insert_message_id             varchar(150),
    Update_message_id             varchar(150),
    EVN_Date                      datetime
)
go
```

**Column Definitions:**

| Column Name                    | Data Type      | Description                                                                                                                                                                   |
| ------------------------------ | -------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| HC_Number                      | varchar(20)    | Health Card Number                                                                                                                                                              |

**Sample Data:**

| HC_Number | QID | Appointment_Id | Initial_Appointment_Date_time | Appointment_Date_time | Appointment_Type_code | Appointment_Status | Clinic_Type_code | Appointment_HC_Code | Appointment_Physician_Id | Appointment_duration | Appointment_Consultation_Type | Insert_Date | Update_Date | Insert_message_id | Update_message_id | EVN_Date |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| HC08806915 | 29765437867 | 184356941 | 2024-03-10 07:15:00.000 | 2024-03-10 07:15:00.000 | Family Physician Video Consultation New | Canceled | LBB Family Med | LBB Leabaib | 51187-Marwa Osman Awad | 20 | null | 2024-10-21 11:19:22.727 | null | Q7486727687T13603975828 | null | 2024-10-21 11:19:17.000 |
| HC08807198 | 23456789101 | 184381241 | 2024-09-09 14:00:00.000 | 2024-09-09 00:00:00.000 | Radiology Ultrasound | Confirmed | RAK UltraSound | RAK Rawdat | null | 20 | null | 2024-09-09 14:00:07.367 | null | Q7486696348T13603933442 | null | null |
| HC08807061 | 28335655590 | 184381245 | 2024-09-11 09:48:33.000 | 2024-09-19 08:00:00.000 | Family Physician New | Confirmed | QUN Family Med | QUN Qatar Univ | 54167-Ehab Said Hamed | 20 | null | 2024-09-11 09:48:53.693 | null | Q7486698636T13603935944 | null | null |
| QID                            | varchar(20)    | Queue ID                                                                                                                                                                   |
| Appointment_Id                 | bigint         | Unique identifier for the appointment (Primary Key)                                                                                                                         |
| Initial_Appointment_Date_time  | datetime       | Date and time of the initial appointment booking                                                                                                                            |
| Appointment_Date_time          | datetime       | Date and time of the scheduled appointment                                                                                                                                  |
| Appointment_Type_code          | varchar(300)   | Code indicating the type of appointment (e.g., check-up, consultation)                                                                                                        |
| Appointment_Status             | varchar(100)   | Status of the appointment (e.g., Scheduled, Confirmed, Cancelled, Completed)                                                                                                   |
| Clinic_Type_code               | varchar(300)   | Code indicating the type of clinic (e.g., General Practice, Specialist Clinic)                                                                                              |
| Appointment_HC_Code            | varchar(100)   | Health Care Code associated with the appointment                                                                                                                             |
| Appointment_Physician_Id       | varchar(300)   | Identifier for the physician associated with the appointment                                                                                                                    |
| Appointment_duration           | int            | Duration of the appointment in minutes (or other unit as defined)                                                                                                             |
| Appointment_Consultation_Type  | varchar(100)   | Type of consultation during the appointment                                                                                                                                 |
| Insert_Date                    | datetime       | Date and time when the record was inserted into the table                                                                                                                     |
| Update_Date                    | datetime       | Date and time when the record was last updated                                                                                                                               |
| Insert_message_id              | varchar(150)   | Identifier of the message that triggered the insertion of the record                                                                                                         |
| Update_message_id              | varchar(150)   | Identifier of the message that triggered the update of the record                                                                                                          |
| EVN_Date                       | datetime       | Event Date related to the appointment booking (often related to HL7 messaging)                                                                                                 |
```
