# POC: HL7 Message Enhancement via Azure Function App

## Objective

Use a function app to **add fields** in the HL7 message **if they do not exist** in the OBX segments.  
Values will be fetched from `HMC_Livefeeds.Patients` table.

### Fields in Scope

- **QID Expiry Date**
- **HC Expiry Date**
- **Assigned Family Physician ID**
- **Assigned HC Code**

---

## Mapping Table

| OBX Identifier   | HL7V2.8 Field                    | FHIR Field                         |
|------------------|----------------------------------|------------------------------------|
| QATAR_ID_EXP     | PID.3.8 - Expiration Date        | Patient.identifier.period.end      |
| HC EXP DATE      | PID.3.8 - Expiration Date        | Patient.identifier.period.end      |
| FAMILY_PHYSICIAN | PID related ROL.4                | Practitioner.name.given[] / family |
| PRIM_ORG_NAME    | PD1.3 - Patient Primary Facility | Organisation.name                  |

> Identifiers are in `['OBX.X']['OBX.3.1']`  
> Values are in `OBX.5.1`

---

## Sample HL7 Message

```hl7
MSH|^~\&|Millennium|HMC|RHAPSODY_ADT|HMC|20250518103740||ADT^A31|Q8980769568T16342892979|P|2.3||||||8859/1
EVN|A31|20250518103740|||50850^Abdelkahliq^Ahmed^Mohamed Khair^^^^^External Id^Personnel^^^External Identifier^""
PID|1||HC06337704^^^MRN^MRN^RHAPSODY_CON_SYS|P4388135^0^""^Passport^Passport^""~DB0CE74AB20E40AB9DD1DBEF25E5ECF7^0^""^CONSUMER_MESSAGING^Messaging^""|SHAIKH^SIRAJ^AHMED WAKEEL HUSAIN^^^^Current~شيخ^فراز^احد شاكيل حسين^^^^Alternate Character Set Current Name||19751114|Male||Non National|^^الدوحة^""^^Qatar^Home^^""~^^^""^^""^Birth^^""~<EMAIL>^^^""^^""^E-mail^^""||~66047513^Pager personal^""|66074513^Business^Tel|English|Married|""||***********|||||||||Indian
PV1||
OBX|1|CE|COUNTRY_RES||Qatar||||||
OBX|2|DT|PASSPT_DAT||20261107||||||
OBX|3|DT|QATAR_ID_EXP||20261030||||||
OBX|4|CE|REGFACILITY||OBK Al Khatab||||||
OBX|5|DT|HC EXP DATE||20280527||||||
OBX|6|CE|PREF_LANGUAG||English||||||
OBX|7|CE|PHCC_GEN_CON||Scanned||||||
OBX|8|ST|PREF_FAC||HG Hamad||||||
OBX|9|TX|PRIM_ORG_NAME||Rawdat Al Khail Health Center||||||
OBX|10|CD|FAMILY_PHYSICIAN||10096519^Wally^Ahmed^Nourelfalah Mahmoud||||||
```

## Implementation Details

### Database Schema

The function will query the `Patients` table to retrieve missing information:

```sql
SELECT 
    QidExpiryDate AS QATAR_ID_EXP,
    HcExpiryDate AS HC_EXP_DATE,
    AssignedFamilyPhysicianId AS FAMILY_PHYSICIAN,
    AssignedHcCode AS PRIM_ORG_NAME
FROM 
    Patients
WHERE 
    HcNumber = '[MRN from PID.3]'
```

### Processing Logic

1. ✅ Parse incoming HL7 message
2. ✅ Extract patient MRN (Health Card Number) from PID.3
3. Check if any required OBX segments are missing
4. Query database for missing information using the HcNumber
5. Add missing OBX segments with appropriate values
6. Return enhanced HL7 message

---

## Expected Output

The function will return the original message with any missing OBX segments added. If all required segments are already present, the message will be returned unchanged.

## Error Handling

- If database connection fails: Log error, return original message
- If a patient is not found: Log warning, return the original message
- If HL7 parsing fails: Log error, return error status
