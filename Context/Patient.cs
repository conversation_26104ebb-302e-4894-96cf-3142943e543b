using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnhanceSIUMessages.Context;

[Table("Patients")]
public class Patient
{
    [Key]
    [Column("QID")] 
    [StringLength(50)] 
    public string? QId { get; set; }

    [Column("QID_Expiry_Date")] 
    public DateTime? QidExpiryDate { get; set; }

    [Column("First_Name_En")]
    [StringLength(150)]
    public string? FirstNameEn { get; set; }

    [Column("Middle_Name_En")]
    [StringLength(150)]
    public string? MiddleNameEn { get; set; }

    [Column("Last_Name_En")]
    [StringLength(150)]
    public string? LastNameEn { get; set; }

    [Column("First_Name_Ar")]
    [StringLength(150)]
    public string? FirstNameAr { get; set; }

    [Column("Middle_Name_Ar")]
    [StringLength(150)]
    public string? MiddleNameAr { get; set; }

    [Column("Last_Name_Ar")]
    [StringLength(150)]
    public string? LastNameAr { get; set; }

    [Column("DOB")] 
    public DateTime? DateOfBirth { get; set; }

    [Column("Nationality_Code")]
    [StringLength(50)]
    public string? NationalityCode { get; set; }

    [Column("Gender_Code")]
    [StringLength(50)]
    public string? GenderCode { get; set; }

    [Column("Mobile_Number")]
    [StringLength(50)]
    public string? MobileNumber { get; set; }

    [Column("HC_Number")]
    [StringLength(50)]
    public string? HcNumber { get; set; }

    [Column("HC_Expiry_Date")] 
    public DateTime? HcExpiryDate { get; set; }

    [Column("GIS_Address_Street")]
    [StringLength(150)]
    public string? GisAddressStreet { get; set; }

    [Column("GIS_Address_Building")]
    [StringLength(150)]
    public string? GisAddressBuilding { get; set; }

    [Column("GIS_Address_Zone")]
    [StringLength(150)]
    public string? GisAddressZone { get; set; }

    [Column("GIS_Address_Unit")]
    [StringLength(150)]
    public string? GisAddressUnit { get; set; }

    [Column("Assigned_Family_Physician_Id")]
    [StringLength(150)]
    public string? AssignedFamilyPhysicianId { get; set; }

    [Column("Assigned_HC_Code")]
    [StringLength(150)]
    public string? AssignedHcCode { get; set; }

    [Column("Marital_Status_Code")]
    [StringLength(50)]
    public string? MaritalStatusCode { get; set; }

    [Column("Insert_Date")] 
    public DateTime? InsertDate { get; set; }

    [Column("Update_date")] 
    public DateTime? UpdateDate { get; set; }

    [Column("Insert_message_id")]
    [StringLength(150)]
    public string? InsertMessageId { get; set; }

    [Column("Update_message_id")]
    [StringLength(150)]
    public string? UpdateMessageId { get; set; }
        
    [Column("EVN_Date")]
    public DateTime? EvnDate { get; set; }
        
    public virtual ICollection<AppointmentBooking>? AppointmentBookings { get; set; }
    public virtual ICollection<Visit>? Visits { get; set; }
}