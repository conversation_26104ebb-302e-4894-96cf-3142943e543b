using Microsoft.EntityFrameworkCore;

namespace EnhanceSIUMessages.Context;

public class HmcLiveFeedService
{
    private readonly HmcLiveFeedContext _context;

    public HmcLiveFeedService(HmcLiveFeedContext context)
    {
        _context = context;
    }

    // Patient related queries
    public async Task<Patient?> GetPatientByQIdAsync(string qId)
    {
        return await _context.Patients
            .FirstOrDefaultAsync(p => p.QId == qId);
    }

    public async Task<Patient?> GetPatientByHcNumberAsync(string hcNumber)
    {
        return await _context.Patients
            .FirstOrDefaultAsync(p => p.HcNumber == hcNumber);
    }
}