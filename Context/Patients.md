## Patients Table Definition

**Table:** Patients

---

This table stores information about patients.

**Schema Definition:**

```sql

create table Patients
(
    QID                          nvarchar(50),
    QID_Expiry_Date              datetime,
    First_Name_En                varchar(150),
    Middle_Name_En               varchar(150),
    Last_Name_En                 varchar(150),
    First_Name_Ar                nvarchar(150),
    Middle_Name_Ar               nvarchar(150),
    Last_Name_Ar                 nvarchar(150),
    DOB                          datetime,
    Nationality_Code             varchar(50),
    Gender_Code                  varchar(50),
    Mobile_Number                varchar(50),
    HC_Number                    nvarchar(50) not null
        constraint PK_Patients
            primary key,
    HC_Expiry_Date               datetime,
    GIS_Address_Street           varchar(150),
    GIS_Address_Building         varchar(150),
    GIS_Address_Zone             varchar(150),
    GIS_Address_Unit             varchar(150),
    Assigned_Family_Physician_Id varchar(150),
    Assigned_HC_Code             varchar(150),
    Marital_Status_Code          varchar(50),
    Insert_Date                  datetime,
    Update_date                  datetime,
    Insert_message_id            varchar(150),
    Update_message_id            varchar(150),
    EVN_Date                     datetime
)
go
```

**Column Definitions:**

| Column Name                  | Data Type     | Description                                                                    |
|------------------------------|---------------|--------------------------------------------------------------------------------|
| QID                          | nvarchar(50)  | Qatar ID                                                                       |
| QID_Expiry_Date              | datetime      | Expiry Date of Qatar ID                                                        |
| First_Name_En                | varchar(150)  | Patient's First Name in English                                                |
| Middle_Name_En               | varchar(150)  | Patient's Middle Name in English                                               |
| Last_Name_En                 | varchar(150)  | Patient's Last Name in English                                                 |
| First_Name_Ar                | nvarchar(150) | Patient's First Name in Arabic                                                 |
| Middle_Name_Ar               | nvarchar(150) | Patient's Middle Name in Arabic                                                |
| Last_Name_Ar                 | nvarchar(150) | Patient's Last Name in Arabic                                                  |
| DOB                          | datetime      | Patient's Date of Birth                                                        |
| Nationality_Code             | varchar(50)   | Code representing the patient's nationality                                    |
| Gender_Code                  | varchar(50)   | Code representing the patient's gender                                         |
| Mobile_Number                | varchar(50)   | Patient's mobile phone number                                                  |
| HC_Number                    | nvarchar(50)  | Patient's Health Card Number (Primary Key)                                     |
| HC_Expiry_Date               | datetime      | Expiry date of the Health Card                                                 |
| GIS_Address_Street           | varchar(150)  | Street portion of the patient's address                                        |
| GIS_Address_Building         | varchar(150)  | Building portion of the patient's address                                      |
| GIS_Address_Zone             | varchar(150)  | Zone portion of the patient's address                                          |
| GIS_Address_Unit             | varchar(150)  | Unit portion of the patient's address (e.g., apartment number)                 |
| Assigned_Family_Physician_Id | varchar(150)  | Identifier for the physician assigned to the patient as their family physician |
| Assigned_HC_Code             | varchar(150)  | Health Care Code assigned to the patient                                       |
| Marital_Status_Code          | varchar(50)   | Code representing the patient's marital status                                 |
| Insert_Date                  | datetime      | Date and time when the record was inserted into the table                      |
| Update_date                  | datetime      | Date and time when the record was last updated                                 |
| Insert_message_id            | varchar(150)  | Identifier of the message that triggered the insertion of the record           |
| Update_message_id            | varchar(150)  | Identifier of the message that triggered the update of the record              |
| EVN_Date                     | datetime      | Event Date related to the patient record (often related to HL7 messaging)      |

