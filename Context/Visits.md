```markdown
## Visits Table Definition

**Table:** Visits

---

This table stores information about patient visits.

**Schema Definition:**

```sql
-- auto-generated definition
create table Visits
(
    HC_Number           varchar(20),
    QID                 varchar(20),
    Visit_Number        bigint not null
        constraint PK_Visits
            primary key,
    FIN_Number          bigint,
    Visit_Date_time     datetime,
    Visit_HC            varchar(100),
    Visit_Type          varchar(300),
    Visit_Clinic        varchar(300),
    Attending_Physician varchar(300),
    Healthcare_Service  varchar(100),
    Admit_Source        varchar(100),
    Insert_Date         datetime,
    Update_Date         datetime,
    Insert_message_id   varchar(150),
    Update_message_id   varchar(150),
    EVN_Date            datetime
)
go
```

**Column Definitions:**

| Column Name           | Data Type      | Description                                                                                                                                       |
| --------------------- | -------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| HC_Number             | varchar(20)    | Health Card Number                                                                                                                                  |

**Sample Data:**

| HC_Number | QID | Visit_Number | FIN_Number | Visit_Date_time | Visit_HC | Visit_Type | Visit_Clinic | Attending_Physician | Healthcare_Service | Admit_Source | Insert_Date | Update_Date | Insert_message_id | Update_message_id | EVN_Date |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| HC08807199 | 34567891011 | 2345679800000001 | 23456798 | 2024-05-02 09:29:49.000 | HG Hamad | Outpatient | AK HIA Midfield | -SECURE  CERNER | null | null | 2024-10-03 10:49:03.950 | 2024-10-15 09:35:18.000 | Q7486711846T13603954162 | Q7486711846T13603954162 | 2024-10-03 10:49:01.000 |
| HC08806997 | 28235625139 | 15791077500000001 | 157910775 | 2016-08-16 07:30:00.000 | Home Health | Recurring | HHC Mauaither | ONCOLOGIST-Oncologist Cerner TEST | null | null | 2024-09-11 10:54:28.227 | 2024-09-11 10:55:45.000 | Q7486698665T13603936032 | Q7486698669T13603936054 | null |
| HC08807468 | 23412341878 | 15791168100000001 | 157911681 | 2024-09-15 08:56:22.000 | HG Hamad | Outpatient | HG MCRC Ped Post Op | PHYSPHCCH-PHCC Physician HIGH 1  TEST | null | null | 2024-09-15 08:57:21.200 | 2024-09-27 05:54:45.000 | Q7486699160T13603936962 | Q7486710483T13603951522 | null |
| QID                   | varchar(20)    | Qatar ID                                                                                                                                          |
| Visit_Number          | bigint         | Unique identifier for the visit (Primary Key)                                                                                                        |
| FIN_Number            | bigint         | Financial Number associated with the visit                                                                                                          |
| Visit_Date_time       | datetime       | Date and time of the visit                                                                                                                          |
| Visit_HC              | varchar(100)   | Health Care code associated with the visit                                                                                                          |
| Visit_Type            | varchar(300)   | Type of visit (e.g., outpatient, inpatient, emergency)                                                                                             |
| Visit_Clinic          | varchar(300)   | Clinic where the visit occurred                                                                                                                   |
| Attending_Physician   | varchar(300)   | Identifier for the physician attending the patient during the visit                                                                                 |
| Healthcare_Service    | varchar(100)   | The specific healthcare service provided during the visit                                                                                          |
| Admit_Source          | varchar(100)   | Source of admission (e.g., emergency room, referral)                                                                                              |
| Insert_Date           | datetime       | Date and time when the record was inserted into the table                                                                                          |
| Update_Date           | datetime       | Date and time when the record was last updated                                                                                                    |
| Insert_message_id     | varchar(150)   | Identifier of the message that triggered the insertion of the record                                                                              |
| Update_message_id     | varchar(150)   | Identifier of the message that triggered the update of the record                                                                               |
| EVN_Date              | datetime       | Event Date related to the visit (often related to HL7 messaging)                                                                                |
```
