namespace EnhanceSIUMessages;

public class SiuMessage
{
    // MSH Segment
    public string? MSH_FieldSeparator { get; set; }
    public string? MSH_EncodingCharacters { get; set; }
    public string? MSH_SendingApplication { get; set; }
    public string? MSH_SendingFacility { get; set; }
    public string? MSH_ReceivingApplication { get; set; }
    public string? MSH_ReceivingFacility { get; set; }
    public string? MSH_DateTimeOfMessage { get; set; }
    public string? MSH_MessageType { get; set; }
    public string? MSH_MessageControlId { get; set; }
    public string? MSH_ProcessingId { get; set; }
    
    // PID Segment
    public string? PID_SetId { get; set; }
    public string? PID_PatientId { get; set; }
    public string? PID_PatientName { get; set; }
    public string? PID_DateTimeOfBirth { get; set; }
    public string? PID_AdministrativeSex { get; set; }
    
    // PV1 Segment
    public string? PV1_SetId { get; set; }
    public string? PV1_PatientClass { get; set; }
    public string? PV1_AssignedPatientLocation { get; set; }
    public string? PV1_AttendingDoctor { get; set; }
    
    // OBX Segments (multiple)
    public List<ObxEntry> OBX { get; set; } = new();
}

public class ObxEntry
{
    public string? SetId { get; set; }
    public string? ValueType { get; set; }
    public string? ObservationIdentifier { get; set; }
    public string? ObservationValue { get; set; }
}
