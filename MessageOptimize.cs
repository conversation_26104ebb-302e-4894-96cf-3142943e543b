using System.Text;
using Azure.Storage.Blobs;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Efferent.HL7.V2;

namespace EnhanceSIUMessages;

/// <summary>
/// HL7 Message Enhancement System
///
/// OBJECTIVE:
/// This Azure Function App enhances HL7 messages by adding missing OBX segments
/// with patient data retrieved from the HMC_Livefeeds.Patients database table.
/// The enhanced messages are saved locally for audit and processing trails.
///
/// PROCESSING WORKFLOW:
///
/// Step 1: HL7 Message Reception and Local Storage
/// 1. Receives HL7 messages via Azure Blob Trigger from "incoming-hl7" container
/// 2. Saves original HL7 message to local "hl7-messages/original" folder
/// 3. Parses the HL7 message using HL7-V2 library for validation
///
/// Step 2: Patient Data Extraction and Database Query
/// 1. Extracts patient MRN (Health Card Number) from PID.3 segment
/// 2. Queries HMC_Livefeeds.Patients table using the extracted HcNumber
/// 3. Retrieves missing patient data: QidExpiryDate, HcExpiryDate, AssignedFamilyPhysicianId, AssignedHcCode
///
/// Step 3: OBX Segment Analysis and Enhancement
/// 1. Analyzes existing OBX segments to identify missing required fields:
///    * QATAR_ID_EXP - Qatar ID expiration date
///    * HC_EXP_DATE - Healthcare card expiration date
///    * FAMILY_PHYSICIAN - Primary care physician information
///    * PRIM_ORG_NAME - Primary healthcare organization name
/// 2. Adds missing OBX segments with appropriate data types and values from database
///
/// Step 4: Enhanced Message Generation and Storage
/// 1. Constructs enhanced HL7 message with all original segments plus missing OBX segments
/// 2. Saves enhanced HL7 message to local "hl7-messages/enhanced" folder
/// 3. Returns the enhanced HL7 message for further processing
///
/// ERROR HANDLING:
/// - Database connection failures: Log error, return original message
/// - Patient not found: Log warning, return original message unchanged
/// - HL7 parsing failures: Log error, return error status
/// - File I/O errors: Log error but continue processing
///
/// TECHNICAL IMPLEMENTATION:
/// - Uses Azure Blob Trigger for automatic processing of new HL7 files
/// - Leverages Efferent.HL7.V2 library for HL7 message parsing and construction
/// - Integrates with Entity Framework Core for database operations
/// - Implements local file storage for audit trails and debugging
/// - Includes comprehensive error handling and logging for production reliability
/// </summary>
public class MessageOptimize
{
    private readonly ILogger<MessageOptimize>? _logger;
    public BlobServiceClient ServiceClient { get; }
    private readonly string? _outputDirectory;
    
    public MessageOptimize(ILogger<MessageOptimize> logger, IConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        var configuration1 = configuration ?? throw new ArgumentNullException(nameof(configuration));
        var connectionString = configuration1["AzureWebJobsStorage"];
        if (string.IsNullOrWhiteSpace(connectionString))
            throw new InvalidOperationException("AzureWebJobsStorage connection string is missing from configuration.");
        ServiceClient = new BlobServiceClient(connectionString);
        
        // Set output directory for JSON files
        _outputDirectory = Path.Combine(AppContext.BaseDirectory, "hl7-json-output");
        Directory.CreateDirectory(_outputDirectory); // Ensure directory exists
    }

    public MessageOptimize(BlobServiceClient serviceClient)
    {
        ServiceClient = serviceClient;
        _logger = null;
        _outputDirectory = null;
    }

    [Function(nameof(MessageOptimize))]
    public async Task Run([BlobTrigger("incoming-hl7/{name}", Connection = "AzureWebJobsStorage")] Stream stream, string name)
    {
        ArgumentNullException.ThrowIfNull(stream);
        ArgumentException.ThrowIfNullOrEmpty(name);

        try
        {
            _logger?.LogInformation("Processing HL7 message: {Name}", name);
            
            // Read HL7 content from the blob stream
            string hl7Content = await ReadStreamContentAsync(stream);
            _logger?.LogInformation("HL7 message content read successfully, length: {Length}", hl7Content.Length);
            
            // Parse the HL7 message using HL7-V2 library
            var hl7Message = ParseHl7Message(hl7Content);
            _logger?.LogInformation("HL7 message parsed successfully");
            
            // Map HL7 message to a strongly typed SIU message object
            var siuMessage = MapToSiuMessage(hl7Message);
            
            // Generate timestamp for unique filenames
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            
            // Save as JSON file
            string outputDir = _outputDirectory ?? Path.Combine(AppContext.BaseDirectory, "hl7-json-output");
            Directory.CreateDirectory(outputDir); // Ensure directory exists
            var jsonFilePath = Path.Combine(outputDir, $"{Path.GetFileNameWithoutExtension(name)}_{timestamp}.json");
            await SaveAsJsonAsync(siuMessage, jsonFilePath);
            
            _logger?.LogInformation("HL7 message converted to JSON and saved at: {JsonPath}", jsonFilePath);
            _logger?.LogInformation("HL7 message processing completed successfully");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to process HL7 message {Name}", name);
            throw new InvalidOperationException($"Error processing HL7 message: {name}", ex);
        }
    }
    
    private static async Task<string> ReadStreamContentAsync(Stream stream)
    {
        using var reader = new StreamReader(stream, Encoding.UTF8, leaveOpen: true);
        return await reader.ReadToEndAsync();
    }
    
    private static Message ParseHl7Message(string hl7Content)
    {
        Message message = new Message(hl7Content);
        bool isParsed = message.ParseMessage();
        if (!isParsed)
        {
            throw new InvalidOperationException("Failed to parse HL7 message using HL7-V2 library.");
        }
        return message;
    }
    
    private static SiuMessage MapToSiuMessage(Message hl7Message)
    {
        var siu = new SiuMessage();
        
        try
        {
            // MSH segment
            siu.MSH_FieldSeparator = hl7Message.GetValue("MSH.1");
            siu.MSH_EncodingCharacters = hl7Message.GetValue("MSH.2");
            siu.MSH_SendingApplication = hl7Message.GetValue("MSH.3");
            siu.MSH_SendingFacility = hl7Message.GetValue("MSH.4");
            siu.MSH_ReceivingApplication = hl7Message.GetValue("MSH.5");
            siu.MSH_ReceivingFacility = hl7Message.GetValue("MSH.6");
            siu.MSH_DateTimeOfMessage = hl7Message.GetValue("MSH.7");
            siu.MSH_MessageType = hl7Message.GetValue("MSH.9");
            siu.MSH_MessageControlId = hl7Message.GetValue("MSH.10");
            siu.MSH_ProcessingId = hl7Message.GetValue("MSH.11");
            
            // PID segment
            siu.PID_SetId = hl7Message.GetValue("PID.1");
            siu.PID_PatientId = hl7Message.GetValue("PID.3.1"); // First component of field 3
            siu.PID_PatientName = hl7Message.GetValue("PID.5.1"); // First component of field 5
            siu.PID_DateTimeOfBirth = hl7Message.GetValue("PID.7");
            siu.PID_AdministrativeSex = hl7Message.GetValue("PID.8");
            
            // PV1 segment
            siu.PV1_SetId = hl7Message.GetValue("PV1.1");
            siu.PV1_PatientClass = hl7Message.GetValue("PV1.2");
            siu.PV1_AssignedPatientLocation = hl7Message.GetValue("PV1.3");
            siu.PV1_AttendingDoctor = hl7Message.GetValue("PV1.7");
            
            // OBX segments (multiple)
            siu.OBX = [];
            
            // Get all OBX segments
            var obxSegments = hl7Message.Segments("OBX");
            for (int i = 0; i < obxSegments.Count; i++)
            {
                // Use the OBX segment index to construct the path
                string obxIndex = (i + 1).ToString();
                
                var entry = new ObxEntry
                {
                    SetId = hl7Message.GetValue($"OBX({obxIndex}).1"),
                    ValueType = hl7Message.GetValue($"OBX({obxIndex}).2"),
                    ObservationIdentifier = hl7Message.GetValue($"OBX({obxIndex}).3"),
                    ObservationValue = hl7Message.GetValue($"OBX({obxIndex}).5")
                };
                siu.OBX.Add(entry);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error mapping HL7 message to SIU: {ex.Message}");
        }
        
        return siu;
    }
    
    private static async Task SaveAsJsonAsync(object data, string filePath)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        
        string jsonContent = JsonSerializer.Serialize(data, options);
        await File.WriteAllTextAsync(filePath, jsonContent);
    }
}
