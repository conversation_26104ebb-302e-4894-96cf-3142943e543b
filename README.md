# EnhanceSIUMessages - Azure Function App

This Azure Function app processes HL7 SIU messages from Azure Blob Storage.

## Troubleshooting Blob Triggers

If your Azure Function is not being triggered by new files in Azure Storage, check the following:

### 1. Container Existence

Ensure the `incoming-hl7` container exists in your Azure Storage account. Azure Functions won't create the container automatically.

### 2. Connection String Configuration

Make sure your `local.settings.json` has the correct connection string for `AzureWebJobsStorage`.

### 3. Blob Trigger Registration

Ensure the function worker is properly configured with middleware for blob triggers in `Program.cs`.

### 4. Testing the Trigger

Use the included `setup-blob-trigger.ps1` script to:
- Create the required container if it doesn't exist
- Upload a test HL7 message to trigger the function

### 5. Checking Logs

When running locally, watch the console logs for any errors. In Azure, check Application Insights or Log Stream.

## Running the Application

1. Start the function app locally:
   ```
   func start
   ```

2. Run the setup script to create the container and upload a test file:
   ```powershell
   .\setup-blob-trigger.ps1
   ```

3. Check the logs to see if the function was triggered.

## Common Issues

- **Cold Start**: The first trigger might take longer due to function app cold start.
- **Polling Delay**: Blob triggers use polling, so there might be a delay before the function is triggered.
- **Permission Issues**: Ensure the function app has access to the storage account.
- **Network Restrictions**: Check if there are any firewall rules preventing access.
