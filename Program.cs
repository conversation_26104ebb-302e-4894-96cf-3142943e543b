using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using EnhanceSIUMessages.Context;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
    .ConfigureAppConfiguration(config => {
        config.AddEnvironmentVariables();
    })
    .ConfigureServices((context, services) => {
        // Add HmcLiveFeedContext to the DI container
        var hmcLiveFeedsConnectionString = context.Configuration.GetConnectionString("HmcLiveFeeds")
            ?? context.Configuration["HmcLiveFeedsConnectionString"];
        services.AddDbContext<HmcLiveFeedContext>(options =>
            options.UseSqlServer(hmcLiveFeedsConnectionString)
        );

        // Add Application Insights
        services.AddApplicationInsightsTelemetryWorkerService()
               .ConfigureFunctionsApplicationInsights();
    })
    .ConfigureLogging(logging => {
        logging.AddConsole();
    })
    .Build();

await host.RunAsync();